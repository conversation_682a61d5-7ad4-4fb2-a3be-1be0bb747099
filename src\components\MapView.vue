<template>
  <div class="map-container">
    <div ref="mapContainer" class="map"></div>

    <!-- 地图控制按钮 -->
    <div class="map-controls">
      <el-button
        type="primary"
        :icon="Plus"
        circle
        @click="toggleAddMode"
        :class="{ active: addMode }"
        title="点击地图添加店铺"
      />
      <el-button
        :icon="Location"
        circle
        @click="centerToUserLocation"
        title="定位到当前位置"
      />
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, computed, watch } from "vue";
import { useStore } from "vuex";
import L from "leaflet";
import { Plus, Location } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";

// 修复 Leaflet 默认图标问题
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: require("leaflet/dist/images/marker-icon-2x.png"),
  iconUrl: require("leaflet/dist/images/marker-icon.png"),
  shadowUrl: require("leaflet/dist/images/marker-shadow.png"),
});

export default {
  name: "MapView",
  components: {},

  setup() {
    const store = useStore();
    const mapContainer = ref(null);
    const map = ref(null);
    const markers = ref(new Map());
    const addMode = ref(false);

    // 计算属性
    const shops = computed(() => store.getters["shops/filteredShops"]);
    const mapCenter = computed(() => store.getters["ui/mapCenter"]);
    const mapZoom = computed(() => store.getters["ui/mapZoom"]);

    // 创建自定义图标
    const createCustomIcon = (category) => {
      const categoryData =
        store.getters["categories/getCategoryByName"](category);
      const color = categoryData?.color || "#409eff";

      return L.divIcon({
        className: "custom-marker",
        html: `
          <div style="
            background-color: ${color};
            width: 25px;
            height: 25px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
          ">
            ${categoryData?.icon || "🍽️"}
          </div>
        `,
        iconSize: [25, 25],
        iconAnchor: [12, 12],
      });
    };

    // 初始化地图
    const initMap = () => {
      if (!mapContainer.value) return;

      map.value = L.map(mapContainer.value).setView(
        mapCenter.value,
        mapZoom.value
      );

      // 添加瓦片图层
      L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
        attribution: "© OpenStreetMap contributors",
      }).addTo(map.value);

      // 地图点击事件
      map.value.on("click", handleMapClick);

      // 地图移动事件
      map.value.on("moveend", () => {
        const center = map.value.getCenter();
        const zoom = map.value.getZoom();
        store.dispatch("ui/setMapState", {
          center: [center.lat, center.lng],
          zoom: zoom,
        });
      });

      // 添加现有店铺标记
      addShopMarkers();
    };

    // 处理地图点击
    const handleMapClick = (e) => {
      if (!addMode.value) return;

      const { lat, lng } = e.latlng;

      // 显示添加店铺表单，并传递坐标
      store.dispatch("ui/showShopForm");

      // 临时存储点击的坐标
      store.commit("ui/SET_TEMP_COORDINATES", { lat, lng });

      // 关闭添加模式
      addMode.value = false;
    };

    // 添加店铺标记
    const addShopMarkers = () => {
      // 清除现有标记
      markers.value.forEach((marker) => {
        map.value.removeLayer(marker);
      });
      markers.value.clear();

      // 添加新标记
      shops.value.forEach((shop) => {
        const marker = L.marker([shop.lat, shop.lng], {
          icon: createCustomIcon(shop.category),
        }).addTo(map.value);

        // 绑定弹窗
        marker.bindPopup(`
          <div class="shop-popup">
            <h4>${shop.name}</h4>
            <p><strong>分类:</strong> ${shop.category}</p>
            <p><strong>地址:</strong> ${shop.address}</p>
            <p><strong>描述:</strong> ${shop.description}</p>
            <div class="popup-actions">
              <button onclick="editShop(${shop.id})" class="edit-btn">编辑</button>
              <button onclick="deleteShop(${shop.id})" class="delete-btn">删除</button>
            </div>
          </div>
        `);

        // 点击标记选中店铺
        marker.on("click", () => {
          store.dispatch("shops/selectShop", shop);
        });

        markers.value.set(shop.id, marker);
      });
    };

    // 切换添加模式
    const toggleAddMode = () => {
      addMode.value = !addMode.value;
      if (addMode.value) {
        ElMessage.info("请在地图上点击选择店铺位置");
      }
    };

    // 定位到用户位置
    const centerToUserLocation = () => {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const { latitude, longitude } = position.coords;
            map.value.setView([latitude, longitude], 15);
            ElMessage.success("定位成功");
          },
          () => {
            ElMessage.error("定位失败，请检查位置权限");
          }
        );
      } else {
        ElMessage.error("浏览器不支持地理定位");
      }
    };

    // 监听店铺变化
    watch(
      shops,
      () => {
        if (map.value) {
          addShopMarkers();
        }
      },
      { deep: true }
    );

    // 全局函数，供弹窗按钮调用
    window.editShop = (shopId) => {
      store.dispatch("ui/showShopForm", shopId);
    };

    window.deleteShop = (shopId) => {
      ElMessageBox.confirm("确定要删除这个店铺吗？", "确认删除", {
        type: "warning",
      })
        .then(() => {
          store.dispatch("shops/deleteShop", shopId);
          ElMessage.success("删除成功");
        })
        .catch(() => {});
    };

    onMounted(() => {
      initMap();
    });

    onUnmounted(() => {
      if (map.value) {
        map.value.remove();
      }
    });

    return {
      mapContainer,
      addMode,
      toggleAddMode,
      centerToUserLocation,
      Plus,
      Location,
    };
  },
};
</script>

<style scoped>
.map-container {
  position: relative;
  height: 100%;
  width: 100%;
}

.map {
  height: 100%;
  width: 100%;
}

.map-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.map-controls .el-button.active {
  background-color: #67c23a;
  border-color: #67c23a;
}

/* 自定义标记样式 */
:deep(.custom-marker) {
  background: transparent !important;
  border: none !important;
}

/* 弹窗样式 */
:deep(.shop-popup) {
  min-width: 200px;
}

:deep(.shop-popup h4) {
  margin: 0 0 10px 0;
  color: #409eff;
}

:deep(.shop-popup p) {
  margin: 5px 0;
  font-size: 12px;
}

:deep(.popup-actions) {
  margin-top: 10px;
  display: flex;
  gap: 5px;
}

:deep(.edit-btn),
:deep(.delete-btn) {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

:deep(.edit-btn) {
  background-color: #409eff;
  color: white;
}

:deep(.delete-btn) {
  background-color: #f56c6c;
  color: white;
}
</style>
