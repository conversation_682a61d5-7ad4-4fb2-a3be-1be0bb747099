import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";

// Element Plus
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";

// Leaflet CSS
import "leaflet/dist/leaflet.css";

const app = createApp(App);

// Register Element Plus icons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

app.use(store).use(router).use(ElementPlus).mount("#app");
